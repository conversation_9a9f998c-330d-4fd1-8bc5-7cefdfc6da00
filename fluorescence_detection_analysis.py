#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
荧光检测方法比较研究 - 精简增强版
- 全部图形开放式坐标轴（仅保留下轴与左轴）
- 删除：性能趋势图、剂量响应相关性图、相关性热图、荧光强度分布箱线图
- Bland–Altman 图增加残差信息（残差均值、SD、RMSE；导出残差）
- 其余保留图统一为 4×3 英寸
- 散点统一“双层气泡”（深色外圈 + 半透明内填充），箱线图无 whisker caps
"""

import os
import platform
import warnings
from datetime import datetime

import numpy as np
import pandas as pd
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import to_rgb
from matplotlib.lines import Line2D

import scipy.stats as stats
from scipy.stats import levene
import statsmodels.api as sm
from statsmodels.formula import Formula
from statsmodels.formula.api import ols
from statsmodels.stats.multicomp import pairwise_tukeyhsd

warnings.filterwarnings('ignore')

# =========================
# 字体 & 全局绘图参数
# =========================
if platform.system() == 'Darwin':  # macOS
    matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'STHeiti', 'Arial']
elif platform.system() == 'Windows':  # Windows
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial']
else:  # Linux
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Arial']

matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.size'] = 12
plt.rcParams['figure.figsize'] = (4, 3)   # 统一：4×3 inch
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# Morandi 配色（3 组）
MORANDI_COLORS = ['#D92629', '#3351A2', '#68BB4A']

# 统一视觉参数
SIG_FONTSIZE   = 18     # 显著性“***”字号
SIG_LINEWIDTH  = 1.6    # 显著性连线线宽
OUTLINE_SIZE   = 48     # 气泡外圈面积（pt^2）
FILL_SIZE      = 28     # 气泡内圈面积
OUTLINE_LW     = 1.2    # 外圈线宽
FILL_ALPHA     = 0.60   # 内圈透明度

def darker(color, factor=0.55):
    """整体压暗颜色；factor∈(0,1)，越小越深。"""
    r, g, b = to_rgb(color)
    return (r * factor, g * factor, b * factor)

def scatter_ring(ax, x, y, color, *,
                 outline_size=OUTLINE_SIZE,
                 fill_size=FILL_SIZE,
                 outline_lw=OUTLINE_LW,
                 fill_alpha=FILL_ALPHA,
                 z=3):
    """双层气泡：深色外圈 + 半透明内填充。"""
    edge = darker(color, 0.55)
    ax.scatter(x, y, s=outline_size,
               facecolors='none', edgecolors=edge,
               linewidths=outline_lw, alpha=1.0, zorder=z)
    ax.scatter(x, y, s=fill_size,
               facecolors=color, edgecolors='none',
               alpha=fill_alpha, zorder=z + 0.1)

def open_spines(ax):
    """开放式坐标轴：仅保留下轴与左轴"""
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_visible(True)
    ax.spines['bottom'].set_visible(True)
    ax.spines['left'].set_linewidth(2)
    ax.spines['bottom'].set_linewidth(2)

class FluorescenceAnalysis:
    def __init__(self, random_seed=42):
        np.random.seed(random_seed)
        self.theoretical_conc = [10000, 40000, 120000, 240000]
        self.groups = ['对照组', '实验组A', '实验组B']
        self.n_replicates = 5
        self.output_dir = self._create_output_dir()

    def _create_output_dir(self):
        timestamp = datetime.now().strftime("%y%m%d%H%M")
        output_dir = f"outputs/{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    # -----------------------------
    # 数据与统计
    # -----------------------------
    def generate_data(self):
        data = []
        for conc in self.theoretical_conc:
            for group in self.groups:
                if group == '对照组':
                    base_accuracy = 0.45
                    precision_cv = 0.08
                elif group == '实验组A':
                    base_accuracy = 0.65
                    precision_cv = 0.05
                else:  # 实验组B
                    base_accuracy = 0.85
                    precision_cv = 0.03

                conc_factor = 1 + (np.log10(conc) - np.log10(self.theoretical_conc[0])) * 0.02
                adjusted_accuracy = base_accuracy * conc_factor
                base_measured = conc * adjusted_accuracy

                for rep in range(self.n_replicates):
                    noise = np.random.normal(0, precision_cv)
                    measured_value = base_measured * (1 + noise)
                    measured_value = max(measured_value, conc * 0.1)
                    data.append({
                        '理论荧光强度': conc,
                        '检测组别': group,
                        '测量荧光强度': measured_value,
                        '平行样编号': rep + 1
                    })

        self.data = pd.DataFrame(data)
        return self.data

    def descriptive_statistics(self):
        stats_data = []
        for conc in self.theoretical_conc:
            for group in self.groups:
                subset = self.data[(self.data['理论荧光强度'] == conc) &
                                   (self.data['检测组别'] == group)]
                mean_val = subset['测量荧光强度'].mean()
                std_val = subset['测量荧光强度'].std()
                cv_val = (std_val / mean_val) * 100
                stats_data.append({
                    '理论荧光强度': conc,
                    '检测组别': group,
                    '均值': mean_val,
                    '标准差': std_val,
                    '变异系数(%)': cv_val
                })
        self.stats_summary = pd.DataFrame(stats_data)
        self.stats_summary.to_csv(f"{self.output_dir}/描述性统计摘要.csv",
                                  index=False, encoding='utf-8-sig')
        return self.stats_summary

    # -----------------------------
    # 可视化（保留项）
    # -----------------------------
    def plot_precision_comparison(self):
        """精密度对比（小提琴图）；图幅 4×3；开放式坐标轴"""
        fig, ax = plt.subplots(figsize=(4, 3), dpi=300)

        plot_data, positions, colors, labels = [], [], [], []
        pos = 0
        for i, conc in enumerate(self.theoretical_conc):
            for j, group in enumerate(self.groups):
                subset = self.data[(self.data['理论荧光强度'] == conc) &
                                   (self.data['检测组别'] == group)]
                plot_data.append(subset['测量荧光强度'].values)
                positions.append(pos)
                colors.append(MORANDI_COLORS[j])
                labels.append(f"{conc}-{group}")
                pos += 1
            pos += 0.4  # 浓度间的间隔稍小，适配 4×3

        parts = ax.violinplot(plot_data, positions=positions, widths=0.5,
                              showmeans=True, showmedians=True)
        for i, pc in enumerate(parts['bodies']):
            pc.set_facecolor(colors[i]); pc.set_alpha(0.7)
        for partname in ('cbars', 'cmins', 'cmaxes', 'cmedians', 'cmeans'):
            if partname in parts:
                parts[partname].set_color('black'); parts[partname].set_linewidth(1)

        ax.set_yscale('log')
        ax.set_ylabel('测量荧光强度', fontsize=11)

        # x 轴主刻度为各浓度组中点
        conc_positions = []
        for i, conc in enumerate(self.theoretical_conc):
            start_pos = i * (len(self.groups) + 0.4)
            conc_pos = start_pos + (len(self.groups) - 1) / 2
            conc_positions.append(conc_pos)
        ax.set_xticks(conc_positions)
        ax.set_xticklabels([str(conc) for conc in self.theoretical_conc], fontsize=10)
        ax.set_xlabel('理论荧光强度', fontsize=11)

        ax.grid(True, alpha=0.3, axis='y')
        open_spines(ax)

        # 简洁图例（与颜色一致）
        legend_elements = [patches.Rectangle((0,0),1,1,
                                             facecolor=MORANDI_COLORS[i],
                                             alpha=0.7, label=self.groups[i])
                           for i in range(len(self.groups))]
        ax.legend(handles=legend_elements, loc='upper left', fontsize=9, frameon=False)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/精密度对比图.png", bbox_inches='tight', dpi=300)
        plt.close()
        self.data.to_csv(f"{self.output_dir}/精密度对比图.csv", index=False, encoding='utf-8-sig')

    def correlation_analysis(self):
        """Pearson 相关（仅输出数值文件；不绘图）"""
        rows = []
        for group in self.groups:
            g = self.data[self.data['检测组别'] == group]
            r, p = stats.pearsonr(g['理论荧光强度'], g['测量荧光强度'])
            rows.append({'检测组别': group, 'Pearson相关系数': r, 'P值': p})
        self.correlation_results = pd.DataFrame(rows)
        self.correlation_results.to_csv(f"{self.output_dir}/相关性分析结果.csv",
                                        index=False, encoding='utf-8-sig')
        return self.correlation_results

    def bland_altman_analysis(self):
        """Bland–Altman（含残差信息）；图幅 4×3；开放式坐标轴"""
        rows = []
        for group in self.groups:
            g = self.data[self.data['检测组别'] == group].sort_values(['理论荧光强度', '平行样编号'])
            theo = g['理论荧光强度'].astype(float).values
            meas = g['测量荧光强度'].astype(float).values
            mean_vals = (theo + meas) / 2
            diff_vals = meas - theo                # 差异
            resid = diff_vals - diff_vals.mean()   # 残差（居中）
            rmse = np.sqrt(np.mean(resid**2))      # RMSE

            mean_diff = np.mean(diff_vals)
            std_diff  = np.std(diff_vals, ddof=1)
            loa_upper = mean_diff + 1.96 * std_diff
            loa_lower = mean_diff - 1.96 * std_diff
            n = len(diff_vals)
            se_mean = std_diff / np.sqrt(n)
            t_val = stats.t.ppf(0.975, n - 1)
            ci_upper = mean_diff + t_val * se_mean
            ci_lower = mean_diff - t_val * se_mean

            rows.append({
                '检测组别': group, '平均偏差': mean_diff, '标准差': std_diff,
                '95%一致性界限上限': loa_upper, '95%一致性界限下限': loa_lower,
                '95%置信区间上限': ci_upper, '95%置信区间下限': ci_lower,
                '样本数': n, '残差SD': resid.std(ddof=1), 'RMSE': rmse
            })

            # 绘图
            fig, ax = plt.subplots(figsize=(4, 3), dpi=300)
            scatter_ring(ax, mean_vals, diff_vals, MORANDI_COLORS[self.groups.index(group)], z=3)
            ax.axhline(mean_diff, color='red', linestyle='-', linewidth=2, label='均值差')
            ax.axhline(loa_upper, color='red', linestyle='--', linewidth=1, label='95% LoA')
            ax.axhline(loa_lower, color='red', linestyle='--', linewidth=1)
            ax.axhline(0, color='black', linestyle=':', linewidth=1, alpha=0.6, label='零差线')

            ax.set_xlabel('均值（理论与测量）', fontsize=11)
            ax.set_ylabel('差异（测量−理论）', fontsize=11)
            ax.grid(True, alpha=0.3)
            open_spines(ax)

            # 右侧信息框：加入残差信息
            info = [
                f'均值差: {mean_diff:.2f}',
                f'95% LoA: [{loa_lower:.2f}, {loa_upper:.2f}]',
                f'95% CI: [{ci_lower:.2f}, {ci_upper:.2f}]',
                f'残差SD: {resid.std(ddof=1):.2f}',
                f'RMSE: {rmse:.2f}',
                f'n: {n}'
            ]
            ax.text(1.04, 0.5, '\n'.join(info), transform=ax.transAxes,
                    fontsize=9, va='center',
                    bbox=dict(boxstyle="round,pad=0.4", facecolor='lightgray', alpha=0.25))

            plt.tight_layout()
            out_png = f"{self.output_dir}/Bland-Altman图_{group}_vs_理论值.png"
            plt.savefig(out_png, bbox_inches='tight', dpi=300)
            plt.close()

            # 导出残差数据
            pd.DataFrame({
                '均值': mean_vals, '差异': diff_vals, '残差': resid,
                '理论荧光强度': theo, '测量荧光强度': meas
            }).to_csv(f"{self.output_dir}/Bland-Altman残差_{group}.csv",
                     index=False, encoding='utf-8-sig')

        self.bland_altman_results = pd.DataFrame(rows)
        self.bland_altman_results.to_csv(f"{self.output_dir}/Bland-Altman分析结果.csv",
                                         index=False, encoding='utf-8-sig')
        return self.bland_altman_results

    def statistical_tests(self):
        """ANOVA + Tukey + Levene（与原版一致；不绘图）"""
        anova_data = self.data.copy()
        anova_data['log_measured'] = np.log10(anova_data['测量荧光强度'].astype(float))
        anova_data['log_theoretical'] = np.log10(anova_data['理论荧光强度'].astype(float))
        model = ols('log_measured ~ C(检测组别) * C(log_theoretical)', data=anova_data).fit()
        anova_table = sm.stats.anova_lm(model, typ=2)
        anova_table.to_csv(f"{self.output_dir}/重复测量方差分析结果.csv", encoding='utf-8-sig')

        posthoc_results, significance_data = [], []
        for conc in self.theoretical_conc:
            conc_data = self.data[self.data['理论荧光强度'] == conc].copy()
            try:
                tukey = pairwise_tukeyhsd(endog=conc_data['测量荧光强度'].astype(float),
                                          groups=conc_data['检测组别'], alpha=0.05)
                summary_data = tukey.summary().data[1:]
                for row in summary_data:
                    p_val = float(row[3]); is_sig = row[6] == 'True'
                    if p_val < 0.001:
                        sig_mark = '***'
                    elif p_val < 0.01:
                        sig_mark = '**'
                    elif p_val < 0.05:
                        sig_mark = '*'
                    else:
                        sig_mark = 'ns'
                    posthoc_results.append({
                        '理论荧光强度': conc, '组别1': row[0], '组别2': row[1],
                        '均值差异': float(row[2]), 'P值': p_val,
                        '95%置信区间下限': float(row[4]), '95%置信区间上限': float(row[5]),
                        '显著性': is_sig, '显著性标记': sig_mark
                    })
                    significance_data.append({
                        '理论荧光强度': conc, '比较': f"{row[0]} vs {row[1]}",
                        '显著性标记': sig_mark, 'P值': p_val
                    })
            except Exception as e:
                print(f"浓度 {conc} 的事后比较出现错误: {e}")
                continue

        self.posthoc_results = pd.DataFrame(posthoc_results)
        self.posthoc_results.to_csv(f"{self.output_dir}/事后比较结果.csv",
                                    index=False, encoding='utf-8-sig')
        self.significance_data = pd.DataFrame(significance_data)
        self.significance_data.to_csv(f"{self.output_dir}/显著性标记数据.csv",
                                      index=False, encoding='utf-8-sig')

        arrays = [self.data[self.data['检测组别'] == g]['测量荧光强度'].astype(float).values for g in self.groups]
        lev_stat, lev_p = levene(*arrays)
        levene_result = pd.DataFrame({
            '检验类型': ['Levene方差齐性检验'],
            '统计量': [float(lev_stat)], 'P值': [float(lev_p)],
            '结论': ['方差齐性' if lev_p > 0.05 else '方差不齐性']
        })
        levene_result.to_csv(f"{self.output_dir}/方差齐性检验结果.csv", index=False, encoding='utf-8-sig')
        return anova_table, self.posthoc_results, levene_result

    def plot_significance_comparison(self):
        """分浓度显著性比较散点；图幅 4×3；开放式坐标轴"""
        for idx, conc in enumerate(self.theoretical_conc):
            fig, ax = plt.subplots(figsize=(4, 3), dpi=300)
            conc_data = self.data[self.data['理论荧光强度'] == conc]
            summary_stats = conc_data.groupby('检测组别')['测量荧光强度'].agg(['mean', 'sem']).reset_index()
            x_pos = np.arange(len(self.groups))

            for i, group in enumerate(self.groups):
                g = conc_data[conc_data['检测组别'] == group]
                x_jitter = np.random.normal(i, 0.05, len(g))
                scatter_ring(ax, x_jitter, g['测量荧光强度'],
                             MORANDI_COLORS[i],
                             outline_size=36, fill_size=20, z=3)

            for i, group in enumerate(self.groups):
                gsum = summary_stats[summary_stats['检测组别'] == group]
                ax.errorbar(i, gsum['mean'].iloc[0],
                            yerr=gsum['sem'].iloc[0],
                            fmt='D', color='black', markersize=1,
                            capsize=4, markerfacecolor='white',
                            markeredgewidth=0, linewidth=0, zorder=4)

            ax.set_xticks(x_pos); ax.set_xticklabels(self.groups, fontsize=10)
            ax.set_ylabel('测量荧光强度', fontsize=11)
            ax.grid(True, alpha=0.3, axis='y')
            open_spines(ax)

            if hasattr(self, 'significance_data'):
                conc_sig = self.significance_data[self.significance_data['理论荧光强度'] == conc]
                max_height = max(summary_stats['mean'] + summary_stats['sem'])
                y_offset = max_height * 0.12
                comparisons = [(0, 1, '对照组 vs 实验组A'),
                               (1, 2, '实验组A vs 实验组B'),
                               (0, 2, '对照组 vs 实验组B')]
                for i, (p1, p2, name) in enumerate(comparisons):
                    sig_mark = 'ns'
                    for _, row in conc_sig.iterrows():
                        if name in row['比较'] or row['比较'] in name:
                            sig_mark = row['显著性标记']; break
                    y_pos = max_height + y_offset * (i + 1)
                    ax.plot([p1, p2], [y_pos, y_pos], 'k-', linewidth=SIG_LINEWIDTH)
                    line_h = y_offset * 0.4
                    ax.plot([p1, p1], [y_pos - line_h, y_pos], 'k-', linewidth=SIG_LINEWIDTH)
                    ax.plot([p2, p2], [y_pos - line_h, y_pos], 'k-', linewidth=SIG_LINEWIDTH)
                    ax.text((p1 + p2) / 2, y_pos + y_offset * 0.1, sig_mark,
                            ha='center', va='bottom',
                            fontsize=SIG_FONTSIZE, fontweight='black')

            ax.set_title(f'理论荧光强度: {conc}', fontsize=11, fontweight='bold')
            if hasattr(self, 'significance_data'):
                ax.set_ylim(0, max_height * 1.6)

            plt.tight_layout()
            plt.savefig(f"{self.output_dir}/显著性比较散点图_{conc}.png", bbox_inches='tight', dpi=300)
            plt.close()

        summary_all = self.data.groupby(['理论荧光强度', '检测组别'])['测量荧光强度'].agg(['mean', 'sem']).reset_index()
        summary_all.to_csv(f"{self.output_dir}/显著性比较散点图.csv", index=False, encoding='utf-8-sig')

    def plot_additional_charts(self):
        """仅保留：实际值 vs 理论值散点；检测效率箱线+气泡+显著性"""
        # 1) 实际值 vs 理论值（散点改为双层气泡）
        fig, ax = plt.subplots(figsize=(4, 3), dpi=300)
        for i, group in enumerate(self.groups):
            g = self.data[self.data['检测组别'] == group]
            scatter_ring(ax, g['理论荧光强度'], g['测量荧光强度'], MORANDI_COLORS[i], z=3)
        min_val, max_val = min(self.theoretical_conc), max(self.theoretical_conc)
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.6, label='理想线 (y=x)')
        ax.set_xlabel('理论荧光强度', fontsize=11); ax.set_ylabel('测量荧光强度', fontsize=11)
        ax.grid(True, alpha=0.3)
        open_spines(ax)
        # 与散点一致的图例
        legend_handles = [Line2D([0], [0], marker='o', linestyle='None',
                                 markersize=7, markerfacecolor=MORANDI_COLORS[i],
                                 markeredgecolor=darker(MORANDI_COLORS[i], 0.55),
                                 markeredgewidth=OUTLINE_LW, alpha=FILL_ALPHA,
                                 label=self.groups[i]) for i in range(len(self.groups))]
        ax.legend(handles=legend_handles, loc='upper left', fontsize=9, frameon=False)
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/实际值vs理论值散点图对比.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 2) 检测效率对比（箱线 + 双层气泡 + 显著性）
        fig, ax = plt.subplots(figsize=(4, 3), dpi=300)
        efficiency_data = []
        for group in self.groups:
            gd = self.data[self.data['检测组别'] == group]
            ratios = gd['测量荧光强度'] / gd['理论荧光强度']
            efficiency_data.extend([(group, r) for r in ratios])
        efficiency_df = pd.DataFrame(efficiency_data, columns=['组别', '效率'])
        summary_stats = efficiency_df.groupby('组别')['效率'].agg(['mean', 'std', 'sem']).reset_index()
        x_pos = np.arange(len(self.groups))

        for i, group in enumerate(self.groups):
            vals = efficiency_df[efficiency_df['组别'] == group]['效率'].astype(float).values
            q25, q50, q75 = np.percentile(vals, [25, 50, 75]); iqr = q75 - q25
            lw = q25 - 1.5 * iqr; uw = q75 + 1.5 * iqr
            whisker_min = max(lw, np.min(vals)); whisker_max = min(uw, np.max(vals))
            outliers = vals[(vals < lw) | (vals > uw)]

            box_width = 0.30
            box = patches.Rectangle((i - box_width/2, q25), box_width, iqr,
                                    linewidth=1.5, edgecolor='black',
                                    facecolor=MORANDI_COLORS[i], alpha=0.7, zorder=1)
            ax.add_patch(box)
            ax.plot([i - box_width/2, i + box_width/2], [q50, q50],
                    color='black', linewidth=3, alpha=0.9, zorder=2)
            # 无 caps 竖向须
            ax.plot([i, i], [whisker_min, q25], color='black', linewidth=1.5, alpha=0.8, zorder=1.5)
            ax.plot([i, i], [q75, whisker_max], color='black', linewidth=1.5, alpha=0.8, zorder=1.5)

            x_jitter = np.random.normal(i, 0.08, len(vals))
            scatter_ring(ax, x_jitter, vals, MORANDI_COLORS[i], z=3)

            if len(outliers) > 0:
                ox = np.full(len(outliers), i)
                edge_red = darker('red', 0.55)
                ax.scatter(ox, outliers, s=OUTLINE_SIZE,
                           facecolors='none', edgecolors=edge_red,
                           linewidths=OUTLINE_LW, zorder=4.5)
                ax.scatter(ox, outliers, s=FILL_SIZE,
                           facecolors='red', edgecolors='none',
                           alpha=0.85, zorder=4.6)

        # 显著性（两两）
        significance_results = []
        group_pairs = [(0, 1), (1, 2), (0, 2)]
        pair_names = ['对照组 vs 实验组A', '实验组A vs 实验组B', '对照组 vs 实验组B']
        from scipy.stats import ttest_ind
        for (i, j), name in zip(group_pairs, pair_names):
            g1 = efficiency_df[efficiency_df['组别'] == self.groups[i]]['效率'].astype(float).values
            g2 = efficiency_df[efficiency_df['组别'] == self.groups[j]]['效率'].astype(float).values
            t_val, p_val = ttest_ind(g1, g2, equal_var=False)
            if p_val < 0.001: sig_mark = '***'
            elif p_val < 0.01: sig_mark = '**'
            elif p_val < 0.05: sig_mark = '*'
            else: sig_mark = 'ns'
            significance_results.append({'比较组合': name, 't统计量': t_val, 'P值': p_val, '显著性标记': sig_mark})

        max_height = efficiency_df['效率'].max()
        y_offset = max_height * 0.12
        for idx, ((i, j), res) in enumerate(zip(group_pairs, significance_results)):
            y_pos = max_height + y_offset * (idx + 1)
            ax.plot([i, j], [y_pos, y_pos], 'k-', linewidth=SIG_LINEWIDTH)
            ax.plot([i, i], [y_pos - y_offset*0.35, y_pos], 'k-', linewidth=SIG_LINEWIDTH)
            ax.plot([j, j], [y_pos - y_offset*0.35, y_pos], 'k-', linewidth=SIG_LINEWIDTH)
            ax.text((i + j) / 2, y_pos + y_offset*0.1, res['显著性标记'],
                    ha='center', va='bottom', fontsize=SIG_FONTSIZE, fontweight='black')

        ax.set_xticks(x_pos); ax.set_xticklabels(self.groups, fontsize=10)
        ax.set_ylabel('检测效率', fontsize=11)
        ax.grid(True, alpha=0.3, axis='y')
        open_spines(ax)
        ax.set_ylim(0, max_height * 1.35)

        # 与散点一致的图例
        legend_handles = [Line2D([0], [0], marker='o', linestyle='None',
                                 markersize=7, markerfacecolor=MORANDI_COLORS[i],
                                 markeredgecolor=darker(MORANDI_COLORS[i], 0.55),
                                 markeredgewidth=OUTLINE_LW, alpha=FILL_ALPHA,
                                 label=self.groups[i]) for i in range(len(self.groups))]
        ax.legend(handles=legend_handles, loc='upper left', fontsize=9, frameon=False)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/检测效率对比散点图.png", bbox_inches='tight', dpi=300)
        plt.close()

        efficiency_df.to_csv(f"{self.output_dir}/检测效率数据.csv", index=False, encoding='utf-8-sig')
        summary_stats.to_csv(f"{self.output_dir}/检测效率统计摘要.csv", index=False, encoding='utf-8-sig')
        pd.DataFrame(significance_results).to_csv(f"{self.output_dir}/检测效率显著性检验结果.csv",
                                                 index=False, encoding='utf-8-sig')

    # -----------------------------
    # 文本报告 & Markdown 汇总
    # -----------------------------
    def generate_summary_report(self):
        report = []
        report.append("=== 荧光检测方法比较研究 - 完整分析报告（精简版） ===\n")
        report.append("【研究概述】")
        report.append("本研究基于模拟数据比较三种荧光检测方法的性能：对照组、实验组A、实验组B。")
        report.append(f"共分析了 {len(self.data)} 个数据点，涵盖 4 个理论荧光强度水平，每组 5 个平行样。\n")

        report.append("【1. 精密度分析】")
        for group in self.groups:
            g = self.stats_summary[self.stats_summary['检测组别'] == group]
            mean_cv = g['变异系数(%)'].mean()
            report.append(f"   {group}: 平均CV = {mean_cv:.2f}%")
        report.append("")

        report.append("【2. 准确度（相关性）】")
        for _, row in self.correlation_results.iterrows():
            report.append(f"   {row['检测组别']}: r = {row['Pearson相关系数']:.3f}, p = {row['P值']:.2e}")
        report.append("")

        report.append("【3. 方法一致性（Bland–Altman，含残差）】")
        for _, row in self.bland_altman_results.iterrows():
            report.append(f"   {row['检测组别']} vs 理论值: 均值差 = {row['平均偏差']:.2f}, "
                          f"95%LoA = [{row['95%一致性界限下限']:.2f}, {row['95%一致性界限上限']:.2f}], "
                          f"残差SD = {row['残差SD']:.2f}, RMSE = {row['RMSE']:.2f}")
        report.append("")

        with open(f"{self.output_dir}/完整分析报告.txt", 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        # 简要版
        simple = []
        simple.append("=== 荧光检测方法比较研究 - 分析摘要报告（精简） ===\n")
        for g in self.groups:
            gg = self.stats_summary[self.stats_summary['检测组别'] == g]
            simple.append(f"{g}: 平均CV = {gg['变异系数(%)'].mean():.2f}%")
        with open(f"{self.output_dir}/分析摘要报告.txt", 'w', encoding='utf-8') as f:
            f.write('\n'.join(simple))
        return '\n'.join(simple)

    def generate_markdown_overview(self):
        """集中展示保留图像"""
        md = ["# 结果汇总（精简版）\n"]
        def add_img(title, fname):
            path = os.path.join(self.output_dir, fname)
            if os.path.exists(path):
                md.append(f"## {title}\n")
                md.append(f"![{title}]({fname})\n")

        add_img("精密度对比图", "精密度对比图.png")
        for g in self.groups:
            add_img(f"Bland–Altman：{g} vs 理论值", f"Bland-Altman图_{g}_vs_理论值.png")
        for conc in self.theoretical_conc:
            add_img(f"显著性比较散点图（理论荧光强度 {conc}）", f"显著性比较散点图_{conc}.png")
        add_img("实际值 vs 理论值散点图对比", "实际值vs理论值散点图对比.png")
        add_img("检测效率对比散点图", "检测效率对比散点图.png")

        out_md = os.path.join(self.output_dir, "结果汇总.md")
        with open(out_md, "w", encoding="utf-8") as f:
            f.write("\n".join(md))
        return out_md

    # -----------------------------
    # 主流程
    # -----------------------------
    def run_complete_analysis(self):
        print("开始荧光检测方法比较研究分析（精简增强版）...")
        print(f"输出目录: {self.output_dir}")

        print("\n1. 生成模拟数据...")
        self.generate_data(); print(f"   生成数据点数: {len(self.data)}")

        print("\n2. 计算描述性统计...")
        self.descriptive_statistics()

        print("\n3. 生成可视化图表（全部 4×3，开放式坐标轴）...")
        self.plot_precision_comparison(); print("   ✓ 精密度对比图")
        self.correlation_analysis()
        self.bland_altman_analysis(); print("   ✓ Bland–Altman（含残差）")
        self.statistical_tests(); print("   ✓ ANOVA + Tukey + Levene")
        self.plot_significance_comparison(); print("   ✓ 分浓度显著性比较散点")
        self.plot_additional_charts(); print("   ✓ 实际值vs理论值散点 + 检测效率箱点图")

        print("\n4. 生成报告与 Markdown 汇总...")
        self.generate_summary_report()
        md_path = self.generate_markdown_overview()
        print(f"   ✓ Markdown 汇总: {md_path}")

        print(f"\n分析完成！所有结果已保存至: {self.output_dir}")
        return self.output_dir


def main():
    analyzer = FluorescenceAnalysis(random_seed=42)
    output_dir = analyzer.run_complete_analysis()
    print(f"\n所有分析结果已保存至目录: {output_dir}")
    print("\n生成的文件包括:")
    print("- 数据文件: *.csv（含残差导出）")
    print("- 图像文件: *.png（全部 4×3，开放式坐标轴）")
    print("- 文本报告: 分析摘要报告.txt, 完整分析报告.txt")
    print("- Markdown 汇总: 结果汇总.md")


if __name__ == "__main__":
    main()
