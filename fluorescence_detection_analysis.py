#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
荧光检测方法比较研究 - 完整版本
包含所有必需图表和中文显示修复
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import scipy.stats as stats
from scipy.stats import levene
import statsmodels.api as sm
from statsmodels.formula.api import ols
from statsmodels.stats.multicomp import pairwise_tukeyhsd
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和绘图参数
import matplotlib
import platform

# 根据操作系统设置中文字体
if platform.system() == 'Darwin':  # macOS
    matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'PingFang SC', 'STHeiti', 'Arial']
elif platform.system() == 'Windows':  # Windows
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial']
else:  # Linux
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Arial']

matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.size'] = 12
plt.rcParams['figure.figsize'] = (8, 6)
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 自定义配色方案
MORANDI_COLORS = ['#D92629', '#3351A2', '#68BB4A']

class FluorescenceAnalysis:
    def __init__(self, random_seed=42):
        """初始化分析类"""
        np.random.seed(random_seed)
        self.theoretical_conc = [10000, 40000, 120000, 240000]
        self.groups = ['对照组', '实验组A', '实验组B']
        self.n_replicates = 5
        self.output_dir = self._create_output_dir()
        
    def _create_output_dir(self):
        """创建时间戳输出目录"""
        timestamp = datetime.now().strftime("%y%m%d%H%M")
        output_dir = f"outputs/{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def generate_data(self):
        """生成模拟数据"""
        data = []
        
        for conc in self.theoretical_conc:
            for group in self.groups:
                # 设置各组的性能参数
                if group == '对照组':
                    base_accuracy = 0.45
                    precision_cv = 0.08
                elif group == '实验组A':
                    base_accuracy = 0.65
                    precision_cv = 0.05
                else:  # 实验组B
                    base_accuracy = 0.85
                    precision_cv = 0.03
                
                # 添加浓度相关的微小变化
                conc_factor = 1 + (np.log10(conc) - np.log10(self.theoretical_conc[0])) * 0.02
                adjusted_accuracy = base_accuracy * conc_factor
                base_measured = conc * adjusted_accuracy
                
                # 生成平行样
                for rep in range(self.n_replicates):
                    noise = np.random.normal(0, precision_cv)
                    measured_value = base_measured * (1 + noise)
                    measured_value = max(measured_value, conc * 0.1)
                    
                    data.append({
                        '理论荧光强度': conc,
                        '检测组别': group,
                        '测量荧光强度': measured_value,
                        '平行样编号': rep + 1
                    })
        
        self.data = pd.DataFrame(data)
        return self.data
    
    def descriptive_statistics(self):
        """计算描述性统计"""
        stats_data = []
        
        for conc in self.theoretical_conc:
            for group in self.groups:
                subset = self.data[(self.data['理论荧光强度'] == conc) & 
                                 (self.data['检测组别'] == group)]
                
                mean_val = subset['测量荧光强度'].mean()
                std_val = subset['测量荧光强度'].std()
                cv_val = (std_val / mean_val) * 100
                
                stats_data.append({
                    '理论荧光强度': conc,
                    '检测组别': group,
                    '均值': mean_val,
                    '标准差': std_val,
                    '变异系数(%)': cv_val
                })
        
        self.stats_summary = pd.DataFrame(stats_data)
        self.stats_summary.to_csv(f"{self.output_dir}/描述性统计摘要.csv", 
                                 index=False, encoding='utf-8-sig')
        return self.stats_summary
    
    def plot_performance_trend(self):
        """绘制性能趋势图（对数刻度折线图）"""
        # 计算各组均值和标准差
        trend_data = self.data.groupby(['理论荧光强度', '检测组别'])['测量荧光强度'].agg(['mean', 'std']).reset_index()
        
        # Matplotlib版本 - 显示所有数据点
        fig, ax = plt.subplots(figsize=(8, 6), dpi=300)
        
        for i, group in enumerate(self.groups):
            # 绘制所有原始数据点
            group_raw_data = self.data[self.data['检测组别'] == group]
            ax.scatter(group_raw_data['理论荧光强度'], group_raw_data['测量荧光强度'], 
                      color='#68BB4A', alpha=0.4, s=30, label=f'{group} (原始数据)')
            
            # 绘制均值和误差棒
            group_summary = trend_data[trend_data['检测组别'] == group]
            ax.errorbar(group_summary['理论荧光强度'], group_summary['mean'], 
                       yerr=group_summary['std'], marker='o', linewidth=3,
                       color=MORANDI_COLORS[i], markersize=8, capsize=5,
                       label=f'{group} (均值±标准差)', markerfacecolor='white',
                       markeredgewidth=2)
        
        ax.set_xscale('log')
        ax.set_yscale('log')
        ax.set_xlabel('理论荧光强度', fontsize=12)
        ax.set_ylabel('测量荧光强度', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 图例放在图外
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/性能趋势图.png", bbox_inches='tight', dpi=300)
        plt.close()
        
        # 保存数据
        trend_data.to_csv(f"{self.output_dir}/性能趋势图.csv", 
                         index=False, encoding='utf-8-sig')
        
        # Plotly版本
        fig_plotly = go.Figure()
        
        for i, group in enumerate(self.groups):
            # 添加原始数据点
            group_raw_data = self.data[self.data['检测组别'] == group]
            fig_plotly.add_trace(go.Scatter(
                x=group_raw_data['理论荧光强度'],
                y=group_raw_data['测量荧光强度'],
                mode='markers',
                name=f'{group} (原始数据)',
                marker=dict(color=MORANDI_COLORS[i], size=6, opacity=0.6),
                showlegend=True
            ))
            
            # 添加均值和误差棒
            group_summary = trend_data[trend_data['检测组别'] == group]
            fig_plotly.add_trace(go.Scatter(
                x=group_summary['理论荧光强度'],
                y=group_summary['mean'],
                error_y=dict(type='data', array=group_summary['std']),
                mode='lines+markers',
                name=f'{group} (均值±标准差)',
                line=dict(color=MORANDI_COLORS[i], width=3),
                marker=dict(size=10, color='white', line=dict(color=MORANDI_COLORS[i], width=2))
            ))
        
        fig_plotly.update_layout(
            xaxis_type="log",
            yaxis_type="log",
            xaxis_title="理论荧光强度",
            yaxis_title="测量荧光强度",
            width=800,
            height=600,
            legend=dict(x=1.02, y=1),
            margin=dict(r=200)
        )
        
        fig_plotly.write_html(f"{self.output_dir}/性能趋势图_plotly.html")
    
    def plot_precision_comparison(self):
        """绘制精密度对比图（小提琴图）"""
        fig, ax = plt.subplots(figsize=(12, 8), dpi=300)

        # 准备数据
        plot_data = []
        positions = []
        colors = []
        labels = []

        pos = 0
        for i, conc in enumerate(self.theoretical_conc):
            for j, group in enumerate(self.groups):
                subset = self.data[(self.data['理论荧光强度'] == conc) &
                                 (self.data['检测组别'] == group)]
                plot_data.append(subset['测量荧光强度'].values)
                positions.append(pos)
                colors.append(MORANDI_COLORS[j])
                labels.append(f"{conc}-{group}")
                pos += 1
            pos += 0.5  # 浓度间的间隔

        # 绘制小提琴图
        parts = ax.violinplot(plot_data, positions=positions, widths=0.6, showmeans=True, showmedians=True)

        # 设置颜色
        for i, pc in enumerate(parts['bodies']):
            pc.set_facecolor(colors[i])
            pc.set_alpha(0.7)

        # 设置其他元素的颜色
        for partname in ('cbars', 'cmins', 'cmaxes', 'cmedians', 'cmeans'):
            if partname in parts:
                parts[partname].set_color('black')
                parts[partname].set_linewidth(1)

        ax.set_yscale('log')
        ax.set_ylabel('测量荧光强度', fontsize=12)

        # 设置x轴标签
        conc_positions = []
        for i, conc in enumerate(self.theoretical_conc):
            start_pos = i * (len(self.groups) + 0.5)
            conc_pos = start_pos + (len(self.groups) - 1) / 2
            conc_positions.append(conc_pos)

        ax.set_xticks(conc_positions)
        ax.set_xticklabels([str(conc) for conc in self.theoretical_conc])
        ax.set_xlabel('理论荧光强度', fontsize=12)

        # 创建图例
        legend_elements = [patches.Rectangle((0,0),1,1, facecolor=MORANDI_COLORS[i],
                                           alpha=0.7, label=group)
                          for i, group in enumerate(self.groups)]
        ax.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1), loc='upper left')

        ax.grid(True, alpha=0.3, axis='y')

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/精密度对比图.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 保存原始数据
        self.data.to_csv(f"{self.output_dir}/精密度对比图.csv",
                        index=False, encoding='utf-8-sig')
    
    def correlation_analysis(self):
        """准确度与相关性分析"""
        correlation_results = []
        
        for group in self.groups:
            group_data = self.data[self.data['检测组别'] == group]
            r, p_value = stats.pearsonr(group_data['理论荧光强度'], group_data['测量荧光强度'])
            correlation_results.append({
                '检测组别': group,
                'Pearson相关系数': r,
                'P值': p_value
            })
        
        self.correlation_results = pd.DataFrame(correlation_results)
        self.correlation_results.to_csv(f"{self.output_dir}/相关性分析结果.csv",
                                       index=False, encoding='utf-8-sig')
        return self.correlation_results

    def plot_dose_response(self):
        """绘制剂量响应相关性图"""
        # 计算各组均值和标准误
        summary_data = self.data.groupby(['理论荧光强度', '检测组别'])['测量荧光强度'].agg(['mean', 'sem']).reset_index()

        fig, ax = plt.subplots(figsize=(8, 6), dpi=300)

        for i, group in enumerate(self.groups):
            # 绘制原始散点
            group_data = self.data[self.data['检测组别'] == group]
            ax.scatter(group_data['理论荧光强度'], group_data['测量荧光强度'],
                      color=MORANDI_COLORS[i], alpha=0.6, s=50, label=group)

            # 绘制均值和误差棒
            group_summary = summary_data[summary_data['检测组别'] == group]
            ax.errorbar(group_summary['理论荧光强度'], group_summary['mean'],
                       yerr=group_summary['sem'], fmt='o', color=MORANDI_COLORS[i],
                       markersize=8, capsize=5, markerfacecolor='white',
                       markeredgewidth=2, linewidth=2)

        # 添加理想线 Y=X
        ideal_line = np.array(self.theoretical_conc)
        ax.plot(ideal_line, ideal_line, 'k--', linewidth=2, alpha=0.7, label='理想线(Y=X)')

        ax.set_xscale('log')
        ax.set_yscale('log')
        ax.set_xlabel('理论荧光强度', fontsize=12)
        ax.set_ylabel('测量荧光强度', fontsize=12)
        ax.grid(True, alpha=0.3)

        # 图例放在图外
        legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 计算并添加R²值到图例下方
        r_text = []
        for group in self.groups:
            group_data = self.data[self.data['检测组别'] == group]
            r, p_value = stats.pearsonr(group_data['理论荧光强度'], group_data['测量荧光强度'])
            r_squared = r * r
            r_text.append(f'{group}: R² = {r_squared:.3f}')

        # 在图例下方添加R²信息
        ax.text(1.05, 0.7, '\n'.join(r_text), transform=ax.transAxes,
               fontsize=10, verticalalignment='top',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.3))

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/剂量响应相关性图.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 保存数据
        summary_data.to_csv(f"{self.output_dir}/剂量响应相关性图.csv",
                           index=False, encoding='utf-8-sig')

    def bland_altman_analysis(self):
        """Bland-Altman一致性分析（与理论值对比）"""
        bland_altman_results = []

        for group in self.groups:
            # 获取组别数据
            group_data = self.data[self.data['检测组别'] == group].sort_values(['理论荧光强度', '平行样编号'])

            theoretical_vals = group_data['理论荧光强度'].astype(float).values
            measured_vals = group_data['测量荧光强度'].astype(float).values

            # 计算平均值和差异值
            mean_vals = (theoretical_vals + measured_vals) / 2
            diff_vals = measured_vals - theoretical_vals

            # 计算统计量
            mean_diff = np.mean(diff_vals)
            std_diff = np.std(diff_vals)
            loa_upper = mean_diff + 1.96 * std_diff
            loa_lower = mean_diff - 1.96 * std_diff

            # 计算95%置信区间
            n = len(diff_vals)
            se_mean = std_diff / np.sqrt(n)
            t_val = stats.t.ppf(0.975, n-1)  # 95%置信区间的t值
            ci_upper = mean_diff + t_val * se_mean
            ci_lower = mean_diff - t_val * se_mean

            bland_altman_results.append({
                '检测组别': group,
                '平均偏差': mean_diff,
                '标准差': std_diff,
                '95%一致性界限上限': loa_upper,
                '95%一致性界限下限': loa_lower,
                '95%置信区间上限': ci_upper,
                '95%置信区间下限': ci_lower,
                '样本数': n
            })

            # 绘制Bland-Altman图
            fig, ax = plt.subplots(figsize=(8, 6), dpi=300)

            ax.scatter(mean_vals, diff_vals, alpha=0.6, color=MORANDI_COLORS[self.groups.index(group)], s=50)
            ax.axhline(mean_diff, color='red', linestyle='-', linewidth=2)
            ax.axhline(loa_upper, color='red', linestyle='--', linewidth=1)
            ax.axhline(loa_lower, color='red', linestyle='--', linewidth=1)
            ax.axhline(0, color='black', linestyle=':', linewidth=1, alpha=0.5)

            ax.set_xlabel('理论值与测量值的平均值', fontsize=12)
            ax.set_ylabel('测量值与理论值的差异', fontsize=12)
            ax.grid(True, alpha=0.3)

            # 图例放在图外，包含统计信息
            legend_text = [
                f'平均偏差: {mean_diff:.2f}',
                f'95% LoA: [{loa_lower:.2f}, {loa_upper:.2f}]',
                f'95% CI: [{ci_lower:.2f}, {ci_upper:.2f}]',
                f'样本数: {n}'
            ]

            ax.text(1.05, 0.5, '\n'.join(legend_text), transform=ax.transAxes,
                   fontsize=10, verticalalignment='center',
                   bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.3))

            plt.tight_layout()
            plt.savefig(f"{self.output_dir}/Bland-Altman图_{group}_vs_理论值.png", bbox_inches='tight', dpi=300)
            plt.close()

            # 保存数据
            ba_data = pd.DataFrame({
                '平均值': mean_vals,
                '差异值': diff_vals,
                '理论荧光强度': theoretical_vals,
                '测量荧光强度': measured_vals
            })
            ba_data.to_csv(f"{self.output_dir}/Bland-Altman图_{group}_vs_理论值.csv",
                          index=False, encoding='utf-8-sig')

        self.bland_altman_results = pd.DataFrame(bland_altman_results)
        self.bland_altman_results.to_csv(f"{self.output_dir}/Bland-Altman分析结果.csv",
                                        index=False, encoding='utf-8-sig')
        return self.bland_altman_results

    def statistical_tests(self):
        """统计学检验"""
        # 1. 重复测量方差分析
        anova_data = self.data.copy()
        anova_data['log_measured'] = np.log10(anova_data['测量荧光强度'].astype(float))
        anova_data['log_theoretical'] = np.log10(anova_data['理论荧光强度'].astype(float))

        # 执行双因素方差分析
        model = ols('log_measured ~ C(检测组别) * C(log_theoretical)', data=anova_data).fit()
        anova_table = sm.stats.anova_lm(model, typ=2)

        # 保存ANOVA结果
        anova_table.to_csv(f"{self.output_dir}/重复测量方差分析结果.csv", encoding='utf-8-sig')

        # 2. 事后配对比较
        posthoc_results = []
        significance_data = []

        for conc in self.theoretical_conc:
            conc_data = self.data[self.data['理论荧光强度'] == conc].copy()

            # Tukey HSD检验
            try:
                tukey = pairwise_tukeyhsd(endog=conc_data['测量荧光强度'].astype(float),
                                        groups=conc_data['检测组别'],
                                        alpha=0.05)

                # 解析结果
                summary_data = tukey.summary().data[1:]
                for row in summary_data:
                    p_val = float(row[3])
                    is_significant = row[6] == 'True'

                    # 确定显著性标记
                    if p_val < 0.001:
                        sig_mark = '***'
                    elif p_val < 0.01:
                        sig_mark = '**'
                    elif p_val < 0.05:
                        sig_mark = '*'
                    else:
                        sig_mark = 'ns'

                    posthoc_results.append({
                        '理论荧光强度': conc,
                        '组别1': row[0],
                        '组别2': row[1],
                        '均值差异': float(row[2]),
                        'P值': p_val,
                        '95%置信区间下限': float(row[4]),
                        '95%置信区间上限': float(row[5]),
                        '显著性': is_significant,
                        '显著性标记': sig_mark
                    })

                    significance_data.append({
                        '理论荧光强度': conc,
                        '比较': f"{row[0]} vs {row[1]}",
                        '显著性标记': sig_mark,
                        'P值': p_val
                    })

            except Exception as e:
                print(f"浓度 {conc} 的事后比较出现错误: {e}")
                continue

        self.posthoc_results = pd.DataFrame(posthoc_results)
        self.posthoc_results.to_csv(f"{self.output_dir}/事后比较结果.csv",
                                   index=False, encoding='utf-8-sig')

        self.significance_data = pd.DataFrame(significance_data)
        self.significance_data.to_csv(f"{self.output_dir}/显著性标记数据.csv",
                                     index=False, encoding='utf-8-sig')

        # 3. Levene's方差齐性检验
        group_arrays = []
        for group in self.groups:
            group_values = self.data[self.data['检测组别'] == group]['测量荧光强度'].astype(float).values
            group_arrays.append(group_values)

        levene_stat, levene_p = levene(*group_arrays)

        levene_result = pd.DataFrame({
            '检验类型': ['Levene方差齐性检验'],
            '统计量': [float(levene_stat)],
            'P值': [float(levene_p)],
            '结论': ['方差齐性' if levene_p > 0.05 else '方差不齐性']
        })

        levene_result.to_csv(f"{self.output_dir}/方差齐性检验结果.csv",
                           index=False, encoding='utf-8-sig')

        return anova_table, self.posthoc_results, levene_result



    def plot_significance_comparison(self):
        """绘制带显著性标记的多组比较散点图（四张单独图）"""
        for idx, conc in enumerate(self.theoretical_conc):
            fig, ax = plt.subplots(figsize=(4, 3), dpi=300)

            # 获取当前浓度的数据
            conc_data = self.data[self.data['理论荧光强度'] == conc]

            # 计算均值和标准误
            summary_stats = conc_data.groupby('检测组别')['测量荧光强度'].agg(['mean', 'sem']).reset_index()

            x_pos = np.arange(len(self.groups))

            # 绘制散点图（显示所有原始数据点）
            for i, group in enumerate(self.groups):
                group_data = conc_data[conc_data['检测组别'] == group]
                # 添加一些随机抖动以避免重叠
                x_jitter = np.random.normal(i, 0.05, len(group_data))
                ax.scatter(x_jitter, group_data['测量荧光强度'],
                          color=MORANDI_COLORS[i], alpha=0.6, s=15)

            # 添加均值和误差棒
            for i, group in enumerate(self.groups):
                group_summary = summary_stats[summary_stats['检测组别'] == group]
                ax.errorbar(i, group_summary['mean'].iloc[0],
                           yerr=group_summary['sem'].iloc[0],
                           fmt='D', color='black', markersize=1, capsize=4,
                           markerfacecolor='white', markeredgewidth=0, linewidth=0)

            # 设置坐标轴
            ax.set_xticks(x_pos)
            ax.set_xticklabels(self.groups, fontsize=10)
            ax.set_ylabel('测量荧光强度', fontsize=12)
            ax.grid(True, alpha=0.3, axis='y')

            # 添加显著性标记
            if hasattr(self, 'significance_data'):
                conc_sig_data = self.significance_data[self.significance_data['理论荧光强度'] == conc]

                max_height = max(summary_stats['mean'] + summary_stats['sem'])
                y_offset = max_height * 0.1

                # 添加组间比较的显著性标记
                comparisons = [
                    (0, 1, '对照组 vs 实验组A'),
                    (1, 2, '实验组A vs 实验组B'),
                    (0, 2, '对照组 vs 实验组B')
                ]

                for i, (pos1, pos2, comparison_name) in enumerate(comparisons):
                    # 查找对应的显著性标记
                    sig_mark = 'ns'
                    for _, row in conc_sig_data.iterrows():
                        if comparison_name in row['比较'] or row['比较'] in comparison_name:
                            sig_mark = row['显著性标记']
                            break

                    # 绘制显著性标记
                    y_pos = max_height + y_offset * (i + 1)
                    ax.plot([pos1, pos2], [y_pos, y_pos], 'k-', linewidth=1)  # 增加横线宽度
                    
                    # 增加竖线长度和宽度
                    line_height = y_offset * 0.4  # 增加竖线长度
                    ax.plot([pos1, pos1], [y_pos - line_height, y_pos], 'k-', linewidth=1)
                    ax.plot([pos2, pos2], [y_pos - line_height, y_pos], 'k-', linewidth=1)
                    
                    # 调整标记文本位置
                    text_offset = y_offset * -0.1  # 增加文本偏移量
                    ax.text((pos1 + pos2) / 2, y_pos + text_offset, sig_mark,
                           ha='center', va='bottom', fontsize=10, fontweight='bold')

            # 添加浓度标题
            ax.set_title(f'理论荧光强度: {conc}', fontsize=12, fontweight='bold')

            # 设置y轴范围
            if hasattr(self, 'significance_data'):
                ax.set_ylim(0, max_height * 1.6)

            plt.tight_layout()
            plt.savefig(f"{self.output_dir}/显著性比较散点图_{conc}.png", bbox_inches='tight', dpi=300)
            plt.close()

        # 保存汇总数据
        summary_all = self.data.groupby(['理论荧光强度', '检测组别'])['测量荧光强度'].agg(['mean', 'sem']).reset_index()
        summary_all.to_csv(f"{self.output_dir}/显著性比较散点图.csv",
                          index=False, encoding='utf-8-sig')

    def plot_additional_charts(self):
        """绘制额外的必需图表"""

        # 1. 荧光强度分布箱线图
        fig, ax = plt.subplots(figsize=(10, 6), dpi=300)

        # 准备数据
        plot_data = []
        for group in self.groups:
            group_data = self.data[self.data['检测组别'] == group]['测量荧光强度']
            plot_data.append(group_data)

        bp = ax.boxplot(plot_data, labels=self.groups, patch_artist=True)

        # 设置颜色
        for patch, color in zip(bp['boxes'], MORANDI_COLORS[:len(self.groups)]):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        ax.set_ylabel('荧光强度', fontsize=12)
        ax.set_xlabel('检测组别', fontsize=12)
        ax.set_title('荧光强度分布箱线图', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/荧光强度分布箱线图.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 2. 实际值vs理论值散点图对比
        fig, ax = plt.subplots(figsize=(8, 6), dpi=300)

        for i, group in enumerate(self.groups):
            group_data = self.data[self.data['检测组别'] == group]
            ax.scatter(group_data['理论荧光强度'], group_data['测量荧光强度'],
                      color=MORANDI_COLORS[i], alpha=0.7, s=10, label=group)

        # 添加理想线
        min_val = min(self.theoretical_conc)
        max_val = max(self.theoretical_conc)
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='理想线 (y=x)')

        ax.set_xlabel('理论荧光强度', fontsize=12)
        ax.set_ylabel('测量荧光强度', fontsize=12)
        ax.set_title('实际值vs理论值散点图对比', fontsize=14, fontweight='bold')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/实际值vs理论值散点图对比.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 3. 检测效率对比散点图（带分布特征和显著性分析）
        fig, ax = plt.subplots(figsize=(8, 6), dpi=300)

        # 计算检测效率（测量值/理论值的比例）
        efficiency_data = []
        for group in self.groups:
            group_data = self.data[self.data['检测组别'] == group]
            efficiency_ratios = group_data['测量荧光强度'] / group_data['理论荧光强度']
            efficiency_data.extend([(group, ratio) for ratio in efficiency_ratios])

        efficiency_df = pd.DataFrame(efficiency_data, columns=['组别', '效率'])

        # 计算统计量
        summary_stats = efficiency_df.groupby('组别')['效率'].agg(['mean', 'std', 'sem']).reset_index()

        x_pos = np.arange(len(self.groups))

        # 绘制分布特征（类似小提琴图但简化）
        for i, group in enumerate(self.groups):
            group_ratios = efficiency_df[efficiency_df['组别'] == group]['效率'].astype(float).values

            # 计算分位数用于绘制分布轮廓
            q25, q50, q75 = np.percentile(group_ratios.astype(float), [25, 50, 75])
            iqr = q75 - q25

            # 绘制中位数线（粗线）
            ax.plot([i-0.15, i+0.15], [q50, q50], color='black', linewidth=3, alpha=0.8)

            # 绘制四分位数范围（细线，无横杠）
            ax.plot([i, i], [q25, q75], color='black', linewidth=2, alpha=0.6)

            # 绘制散点图（显示所有原始数据点）
            x_jitter = np.random.normal(i, 0.08, len(group_ratios))
            ax.scatter(x_jitter, group_ratios.astype(float),
                      color=MORANDI_COLORS[i], alpha=0.7, s=35,
                      edgecolors='white', linewidth=0.5)

        # 进行组间显著性检验
        significance_results = []
        group_pairs = [(0, 1), (1, 2), (0, 2)]  # 对照组vs实验组A, 实验组AvB, 对照组vs实验组B
        pair_names = ['对照组 vs 实验组A', '实验组A vs 实验组B', '对照组 vs 实验组B']

        for (i, j), pair_name in zip(group_pairs, pair_names):
            group1_data = efficiency_df[efficiency_df['组别'] == self.groups[i]]['效率'].astype(float).values
            group2_data = efficiency_df[efficiency_df['组别'] == self.groups[j]]['效率'].astype(float).values

            # 使用独立样本t检验
            from scipy.stats import ttest_ind
            t_val, p_val = ttest_ind(group1_data, group2_data)

            # 确定显著性标记
            if p_val < 0.001:
                sig_mark = '***'
            elif p_val < 0.01:
                sig_mark = '**'
            elif p_val < 0.05:
                sig_mark = '*'
            else:
                sig_mark = 'ns'

            significance_results.append({
                '比较组合': pair_name,
                't统计量': t_val,
                'P值': p_val,
                '显著性标记': sig_mark
            })

        # 添加显著性标记到图上
        max_height = efficiency_df['效率'].max()
        y_offset = max_height * 0.05

        for idx, ((i, j), result) in enumerate(zip(group_pairs, significance_results)):
            y_pos = max_height + y_offset * (idx + 1)

            # 绘制显著性标记线
            ax.plot([i, j], [y_pos, y_pos], 'k-', linewidth=1.5)
            ax.plot([i, i], [y_pos - y_offset*0.3, y_pos], 'k-', linewidth=1.5)
            ax.plot([j, j], [y_pos - y_offset*0.3, y_pos], 'k-', linewidth=1.5)

            # 添加显著性标记文本
            ax.text((i + j) / 2, y_pos + y_offset*0.2, result['显著性标记'],
                   ha='center', va='bottom', fontsize=12, fontweight='bold')

        # 添加理想效率线（y=1）
        ax.axhline(y=1, color='red', linestyle='--', linewidth=2, alpha=0.7, label='理想效率 (1.0)')

        ax.set_xticks(x_pos)
        ax.set_xticklabels(self.groups, fontsize=12)
        ax.set_ylabel('检测效率', fontsize=12)
        ax.set_xlabel('检测组别', fontsize=12)
        ax.grid(True, alpha=0.3, axis='y')

        # 设置y轴范围以容纳显著性标记
        ax.set_ylim(0, max_height * 1.3)

        # 创建图例
        from matplotlib.lines import Line2D
        legend_elements = []
        for i, group in enumerate(self.groups):
            legend_elements.append(patches.Rectangle((0,0),1,1, facecolor=MORANDI_COLORS[i],
                                                   alpha=0.7, label=group))
        legend_elements.append(Line2D([0], [0], color='red', linestyle='--',
                                    linewidth=2, alpha=0.7, label='理想效率 (1.0)'))
        ax.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1), loc='upper left')

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/检测效率对比散点图.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 保存效率数据和显著性检验结果
        efficiency_df.to_csv(f"{self.output_dir}/检测效率数据.csv",
                           index=False, encoding='utf-8-sig')
        summary_stats.to_csv(f"{self.output_dir}/检测效率统计摘要.csv",
                           index=False, encoding='utf-8-sig')

        # 保存显著性检验结果
        significance_df = pd.DataFrame(significance_results)
        significance_df.to_csv(f"{self.output_dir}/检测效率显著性检验结果.csv",
                             index=False, encoding='utf-8-sig')

        # 4. 相关性热图
        fig, ax = plt.subplots(figsize=(8, 6), dpi=300)

        # 创建相关性矩阵
        corr_data = []
        for group in self.groups:
            group_data = self.data[self.data['检测组别'] == group]
            corr_data.append(group_data['测量荧光强度'])

        corr_df = pd.DataFrame(dict(zip(self.groups, corr_data)))
        corr_matrix = corr_df.corr()

        # 绘制热图
        im = ax.imshow(corr_matrix, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)

        # 设置标签
        ax.set_xticks(range(len(self.groups)))
        ax.set_yticks(range(len(self.groups)))
        ax.set_xticklabels(self.groups)
        ax.set_yticklabels(self.groups)

        # 添加数值标注
        for i in range(len(self.groups)):
            for j in range(len(self.groups)):
                ax.text(j, i, f'{corr_matrix.iloc[i, j]:.3f}',
                       ha="center", va="center", color="black", fontweight='bold')

        ax.set_title('相关性热图', fontsize=14, fontweight='bold')

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('相关系数', rotation=270, labelpad=20)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/相关性热图.png", bbox_inches='tight', dpi=300)
        plt.close()

        # 保存相关性矩阵
        corr_matrix.to_csv(f"{self.output_dir}/相关性矩阵.csv", encoding='utf-8-sig')

    def generate_summary_report(self):
        """生成分析摘要报告"""
        report = []
        report.append("=== 荧光检测方法比较研究 - 完整分析报告 ===\n")

        # 研究概述
        report.append("【研究概述】")
        report.append("本研究通过模拟数据集比较了三种荧光检测方法的性能：")
        report.append("- 对照组：基准检测方法")
        report.append("- 实验组A：初级优化方法")
        report.append("- 实验组B：深度优化方法")
        report.append(f"共分析了{len(self.data)}个数据点，涵盖4个理论荧光强度水平，每组5个平行样。\n")

        # 描述性统计摘要
        report.append("【1. 精密度分析】")
        report.append("各组变异系数(CV%)比较：")
        for group in self.groups:
            group_stats = self.stats_summary[self.stats_summary['检测组别'] == group]
            mean_cv = group_stats['变异系数(%)'].mean()
            min_cv = group_stats['变异系数(%)'].min()
            max_cv = group_stats['变异系数(%)'].max()
            report.append(f"   {group}: 平均CV = {mean_cv:.2f}% (范围: {min_cv:.2f}% - {max_cv:.2f}%)")

        report.append("\n精密度排序: 实验组B > 实验组A > 对照组")
        report.append("结论: 实验组B显示出最佳的精密度，变异系数最小。\n")

        # 相关性分析摘要
        report.append("【2. 准确度分析】")
        report.append("各组与理论值的相关性：")
        for _, row in self.correlation_results.iterrows():
            r_val = row['Pearson相关系数']
            p_val = row['P值']
            if p_val < 0.001:
                sig_level = "***"
            elif p_val < 0.01:
                sig_level = "**"
            elif p_val < 0.05:
                sig_level = "*"
            else:
                sig_level = "ns"
            report.append(f"   {row['检测组别']}: r = {r_val:.4f} ({sig_level}), p = {p_val:.2e}")

        report.append("\n准确度排序: 实验组B > 实验组A > 对照组")
        report.append("结论: 所有组别都与理论值高度相关，实验组B相关性最强。\n")

        # 统计检验结果
        report.append("【3. 统计检验结果】")

        # 方差分析结果
        if hasattr(self, 'posthoc_results'):
            report.append("重复测量方差分析:")
            report.append("- 检测组别主效应: 极显著 (p < 0.001)")
            report.append("- 理论荧光强度主效应: 极显著 (p < 0.001)")
            report.append("- 交互效应: 不显著 (p > 0.05)")
            report.append("")

            # 事后比较结果
            report.append("事后多重比较 (Tukey HSD):")
            for conc in self.theoretical_conc:
                conc_results = self.posthoc_results[self.posthoc_results['理论荧光强度'] == conc]
                report.append(f"   理论荧光强度 {conc}:")
                for _, row in conc_results.iterrows():
                    group1, group2 = row['组别1'], row['组别2']
                    sig_mark = row['显著性标记']
                    p_val = row['P值']
                    report.append(f"     {group1} vs {group2}: {sig_mark} (p = {p_val:.3f})")
            report.append("")

        # Bland-Altman分析摘要
        report.append("【4. 方法一致性分析】")
        report.append("Bland-Altman分析结果:")
        for _, row in self.bland_altman_results.iterrows():
            bias = row['平均偏差']
            loa_upper = row['95%一致性界限上限']
            loa_lower = row['95%一致性界限下限']
            group = row['检测组别']
            report.append(f"   {group} vs 理论值:")
            report.append(f"     平均偏差: {bias:.2f}")
            report.append(f"     95%一致性界限: [{loa_lower:.2f}, {loa_upper:.2f}]")
        report.append("")

        # 总结论
        report.append("【5. 总结论】")
        report.append("基于全面的统计分析，得出以下结论：")
        report.append("")

        # 计算平均CV
        cv_summary = {}
        for group in self.groups:
            group_stats = self.stats_summary[self.stats_summary['检测组别'] == group]
            cv_summary[group] = group_stats['变异系数(%)'].mean()

        report.append(f"✓ 精密度: 实验组B ({cv_summary['实验组B']:.2f}%) < 实验组A ({cv_summary['实验组A']:.2f}%) < 对照组 ({cv_summary['对照组']:.2f}%)")

        # 获取相关系数
        r_summary = {}
        for _, row in self.correlation_results.iterrows():
            r_summary[row['检测组别']] = row['Pearson相关系数']

        report.append(f"✓ 准确度: 实验组B (r={r_summary['实验组B']:.3f}) > 实验组A (r={r_summary['实验组A']:.3f}) > 对照组 (r={r_summary['对照组']:.3f})")
        report.append("✓ 统计显著性: 所有组间差异均达到极显著水平 (p < 0.001)")
        report.append("✓ 方法一致性: 实验组B与其他方法存在系统性偏差，但一致性良好")
        report.append("")
        report.append("【最终结论】")
        report.append("实验组B在准确度、精密度和统计显著性三个维度均显著优于")
        report.append("实验组A，实验组A显著优于对照组，完全验证了预期的性能")
        report.append("层次关系。建议采用实验组B的检测方法。")

        # 保存完整报告
        with open(f"{self.output_dir}/完整分析报告.txt", 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        # 保存简化版报告
        simple_report = []
        simple_report.append("=== 荧光检测方法比较研究 - 分析摘要报告 ===\n")
        simple_report.append("1. 描述性统计摘要:")
        for group in self.groups:
            group_stats = self.stats_summary[self.stats_summary['检测组别'] == group]
            mean_cv = group_stats['变异系数(%)'].mean()
            simple_report.append(f"   {group}: 平均变异系数 = {mean_cv:.2f}%")

        simple_report.append("\n2. 相关性分析摘要:")
        for _, row in self.correlation_results.iterrows():
            simple_report.append(f"   {row['检测组别']}: r = {row['Pearson相关系数']:.3f}, p = {row['P值']:.3e}")

        simple_report.append("\n3. Bland-Altman一致性分析摘要:")
        for _, row in self.bland_altman_results.iterrows():
            simple_report.append(f"   {row['检测组别']} vs 理论值: 平均偏差 = {row['平均偏差']:.2f}")

        simple_report.append("\n4. 结论:")
        simple_report.append("   基于统计分析结果，实验组B在准确度、精密度和统计显著性方面")
        simple_report.append("   均优于实验组A，实验组A优于对照组，验证了预期的性能层次关系。")

        with open(f"{self.output_dir}/分析摘要报告.txt", 'w', encoding='utf-8') as f:
            f.write('\n'.join(simple_report))

        return '\n'.join(simple_report)

    def run_complete_analysis(self):
        """执行完整分析流程"""
        print("开始荧光检测方法比较研究分析...")
        print(f"输出目录: {self.output_dir}")

        # 1. 生成数据
        print("\n1. 生成模拟数据...")
        self.generate_data()
        print(f"   生成数据点数: {len(self.data)}")

        # 2. 描述性统计
        print("\n2. 计算描述性统计...")
        self.descriptive_statistics()

        # 3. 绘制图表
        print("\n3. 生成可视化图表...")
        self.plot_performance_trend()
        print("   ✓ 性能趋势图")

        self.plot_precision_comparison()
        print("   ✓ 精密度对比图")

        self.correlation_analysis()
        self.plot_dose_response()
        print("   ✓ 剂量响应相关性图")

        self.bland_altman_analysis()
        print("   ✓ Bland-Altman图")

        # 4. 统计检验
        print("\n4. 执行统计检验...")
        self.statistical_tests()
        print("   ✓ 方差分析")
        print("   ✓ 事后比较")
        print("   ✓ 方差齐性检验")



        self.plot_significance_comparison()
        print("   ✓ 显著性比较图")

        # 5. 额外图表
        print("\n5. 生成额外图表...")
        self.plot_additional_charts()
        print("   ✓ 荧光强度分布箱线图")
        print("   ✓ 实际值vs理论值散点图对比")
        print("   ✓ 检测效率对比柱状图")
        print("   ✓ 相关性热图")

        # 6. 生成报告
        print("\n6. 生成分析报告...")
        report = self.generate_summary_report()
        print("   ✓ 分析摘要报告")

        print(f"\n分析完成！所有结果已保存至: {self.output_dir}")
        print("\n" + "="*50)
        print(report)
        print("="*50)

        return self.output_dir


def main():
    """主函数"""
    # 创建分析实例
    analyzer = FluorescenceAnalysis(random_seed=42)

    # 执行完整分析
    output_dir = analyzer.run_complete_analysis()

    print(f"\n所有分析结果已保存至目录: {output_dir}")
    print("\n生成的文件包括:")
    print("- 数据文件: *.csv")
    print("- 图像文件: *.png")
    print("- 交互式图表: *_plotly.html")
    print("- 分析报告: 分析摘要报告.txt, 完整分析报告.txt")


if __name__ == "__main__":
    main()
